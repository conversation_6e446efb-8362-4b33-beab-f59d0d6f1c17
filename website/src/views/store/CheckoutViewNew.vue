<template>
  <div class="checkout-page">
    <!-- Header -->
    <div class="checkout-header">
      <div class="container">
        <h1><i class="fas fa-credit-card"></i> Finaliser la commande</h1>
      </div>
    </div>

    <!-- Main Content -->
    <div class="checkout-main">
      <div class="container">
        <!-- Empty cart notice -->
        <div v-if="cartStore.isEmpty" class="empty-cart">
          <i class="fas fa-shopping-cart"></i>
          <h3>Votre panier est vide</h3>
          <p>Ajoutez des articles à votre panier avant de procéder au checkout</p>
          <router-link to="/pricing" class="btn btn-primary">Voir nos licences</router-link>
        </div>

        <!-- Checkout Layout: 2 colonnes côte à côte -->
        <div v-else class="checkout-grid">
          <!-- Colonne gauche: Formulaire -->
          <div class="checkout-form">
            <!-- Auth Status -->
            <div v-if="authStore.isAuthenticated" class="auth-section">
              <div class="auth-card">
                <div class="auth-info">
                  <i class="fas fa-user-check"></i>
                  <div>
                    <strong>Connecté en tant que {{ authStore.user?.name }}</strong>
                    <p>{{ authStore.user?.email }}</p>
                  </div>
                </div>
                <button @click="logout" class="btn btn-outline btn-sm">
                  <i class="fas fa-sign-out-alt"></i> Se déconnecter
                </button>
              </div>
            </div>

            <!-- Billing Info -->
            <div class="form-section">
              <h2><i class="fas fa-user"></i> Informations de facturation</h2>
              <div class="billing-notice">
                <i class="fas fa-info-circle"></i>
                Les informations de facturation seront automatiquement récupérées depuis votre compte.
              </div>
            </div>

            <!-- Payment Methods -->
            <div class="form-section">
              <h2><i class="fas fa-credit-card"></i> Méthode de paiement</h2>
              <div class="payment-options">
                <label class="payment-option">
                  <input type="radio" name="payment" value="stripe" v-model="selectedPayment">
                  <div class="payment-content">
                    <i class="fas fa-credit-card"></i>
                    <div>
                      <strong>Carte bancaire</strong>
                      <p>Paiement sécurisé par carte bancaire</p>
                    </div>
                  </div>
                </label>
                
                <label class="payment-option">
                  <input type="radio" name="payment" value="paypal" v-model="selectedPayment">
                  <div class="payment-content">
                    <i class="fab fa-paypal"></i>
                    <div>
                      <strong>PayPal</strong>
                      <p>Paiement via PayPal</p>
                    </div>
                  </div>
                </label>
                
                <label class="payment-option">
                  <input type="radio" name="payment" value="bank" v-model="selectedPayment">
                  <div class="payment-content">
                    <i class="fas fa-university"></i>
                    <div>
                      <strong>Virement bancaire</strong>
                      <p>Paiement par virement bancaire</p>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Notes -->
            <div class="form-section">
              <h2><i class="fas fa-comment"></i> Notes (optionnel)</h2>
              <textarea 
                v-model="notes" 
                placeholder="Commentaires ou instructions spéciales..."
                rows="3"
                class="form-textarea">
              </textarea>
            </div>

            <!-- Terms -->
            <div class="form-section">
              <label class="checkbox-label">
                <input type="checkbox" v-model="acceptTerms" required>
                <span>J'accepte les <a href="/terms" target="_blank">conditions générales de vente</a></span>
              </label>
              
              <label class="checkbox-label">
                <input type="checkbox" v-model="acceptNewsletter">
                <span>Je souhaite recevoir les actualités TechCMS par email</span>
              </label>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <router-link to="/store/cart" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Retour au panier
              </router-link>
              <button 
                @click="processOrder" 
                :disabled="!canSubmit" 
                class="btn btn-primary">
                <i class="fas fa-lock"></i> Finaliser la commande
              </button>
            </div>
          </div>

          <!-- Colonne droite: Résumé -->
          <div class="order-summary">
            <div class="summary-card">
              <h3>Résumé de la commande</h3>
              
              <!-- Items -->
              <div class="summary-items">
                <div v-for="item in cartStore.items" :key="item.id" class="summary-item">
                  <div class="item-info">
                    <div class="item-name">{{ item.name }}</div>
                    <div class="item-quantity">Quantité: {{ item.quantity }}</div>
                  </div>
                  <div class="item-price">{{ formatPrice(item.price * item.quantity) }}</div>
                </div>
              </div>

              <!-- Totals -->
              <div class="summary-totals">
                <div class="summary-line">
                  <span>Sous-total</span>
                  <span>{{ formatPrice(cartStore.subtotal) }}</span>
                </div>
                <div class="summary-line">
                  <span>TVA (20%)</span>
                  <span>{{ formatPrice(cartStore.tax) }}</span>
                </div>
                <div class="summary-line total">
                  <span>Total</span>
                  <span>{{ formatPrice(cartStore.total) }}</span>
                </div>
              </div>
            </div>

            <!-- Security Info -->
            <div class="security-info">
              <div class="security-item">
                <i class="fas fa-shield-alt"></i>
                <div>
                  <strong>Paiement sécurisé</strong>
                  <p>Vos données sont protégées par SSL</p>
                </div>
              </div>
              <div class="security-item">
                <i class="fas fa-sync"></i>
                <div>
                  <strong>Activation immédiate</strong>
                  <p>Votre licence sera activée après paiement</p>
                </div>
              </div>
              <div class="security-item">
                <i class="fas fa-headset"></i>
                <div>
                  <strong>Support inclus</strong>
                  <p>Support technique avec votre licence</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import { useAuthStore } from '@/stores/auth'
import { logger } from '@/utils/logger'

const router = useRouter()
const cartStore = useCartStore()
const authStore = useAuthStore()

// Form data
const selectedPayment = ref('stripe')
const notes = ref('')
const acceptTerms = ref(false)
const acceptNewsletter = ref(false)

// Computed
const canSubmit = computed(() => {
  return acceptTerms.value && selectedPayment.value && !cartStore.isEmpty
})

// Methods
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const logout = async () => {
  await authStore.logout()
  router.push('/store/cart')
}

const processOrder = async () => {
  if (!canSubmit.value) return
  
  try {
    logger.info('[Checkout] Processing order...')
    // TODO: Implement order processing
    alert('Commande en cours de traitement...')
  } catch (error) {
    logger.error('[Checkout] Error processing order:', error)
    alert('Erreur lors du traitement de la commande')
  }
}

onMounted(() => {
  logger.info('[Checkout] Page loaded')
})
</script>

<style scoped>
.checkout-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

.checkout-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem 0;
}

.checkout-header h1 {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.checkout-header i {
  margin-right: 0.75rem;
  color: var(--primary-color);
}

.checkout-main {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* DISPOSITION HORIZONTALE - 2 COLONNES */
.checkout-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Colonne gauche - Formulaire */
.checkout-form {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color);
}

/* Colonne droite - Résumé */
.order-summary {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color);
  position: sticky;
  top: 2rem;
}

/* Auth Section */
.auth-section {
  margin-bottom: 2rem;
}

.auth-card {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auth-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.auth-info i {
  color: var(--success-color);
  font-size: 1.5rem;
}

.auth-info strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.25rem;
}

.auth-info p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* Form Sections */
.form-section {
  margin-bottom: 2rem;
}

.form-section h2 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-section h2 i {
  color: var(--primary-color);
}

.billing-notice {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.billing-notice i {
  color: var(--info-color);
}

/* Payment Options */
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-option {
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.payment-option:hover {
  border-color: var(--primary-color);
}

.payment-option:has(input:checked) {
  border-color: var(--primary-color);
  background: var(--primary-color-alpha);
}

.payment-option input[type="radio"] {
  margin: 0;
}

.payment-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.payment-content i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.payment-content strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.25rem;
}

.payment-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* Form Elements */
.form-textarea {
  width: 100%;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-top: 0.25rem;
}

.checkbox-label span {
  color: var(--text-secondary);
  line-height: 1.5;
}

.checkbox-label a {
  color: var(--primary-color);
  text-decoration: none;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  margin-top: 2rem;
}

/* Summary Card */
.summary-card {
  margin-bottom: 2rem;
}

.summary-card h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.summary-items {
  margin-bottom: 1.5rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
}

.item-info .item-name {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.item-info .item-quantity {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.item-price {
  color: var(--text-primary);
  font-weight: 600;
}

.summary-totals {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
}

.summary-line.total {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  border-top: 1px solid var(--border-color);
  padding-top: 0.75rem;
  margin-top: 0.75rem;
  margin-bottom: 0;
}

/* Security Info */
.security-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.security-item i {
  color: var(--success-color);
  font-size: 1.25rem;
}

.security-item strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.security-item p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.8rem;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.empty-cart i {
  font-size: 4rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.empty-cart h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.empty-cart p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
  .checkout-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .order-summary {
    position: static;
  }

  .form-actions {
    flex-direction: column;
  }

  .checkout-header h1 {
    font-size: 1.5rem;
  }
}
</style>
