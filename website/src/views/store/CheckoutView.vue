<template>
  <div class="checkout-view">
    <!-- En-tête -->
    <div class="checkout-header">
      <div class="container">
        <h1 class="page-title">
          <i class="fas fa-credit-card"></i>
          Finaliser la commande
        </h1>
        <div class="checkout-steps">
          <div class="step active">
            <span class="step-number">1</span>
            <span class="step-label">Informations</span>
          </div>
          <div class="step">
            <span class="step-number">2</span>
            <span class="step-label">Paiement</span>
          </div>
          <div class="step">
            <span class="step-number">3</span>
            <span class="step-label">Confirmation</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Contenu -->
    <div class="checkout-content">
      <div class="container">
        <!-- Redirection si panier vide -->
        <div v-if="cartStore.isEmpty" class="empty-cart-notice">
          <i class="fas fa-shopping-cart"></i>
          <h3>Votre panier est vide</h3>
          <p>Ajoutez des articles à votre panier avant de procéder au checkout</p>
          <router-link to="/pricing" class="btn btn-primary">
            Voir nos licences
          </router-link>
        </div>

        <!-- LAYOUT HORIZONTAL INFAILLIBLE -->
        <div v-else class="checkout-container-horizontal horizontal-layout-forced" style="display: flex; flex-direction: row; gap: 3rem; align-items: flex-start; width: 100%; flex-wrap: nowrap;">
          <!-- COLONNE GAUCHE : Formulaire -->
          <div class="checkout-left left-column-forced" style="flex: 1; min-width: 0; max-width: none; width: auto;">
            <!-- Onglets d'authentification (si non connecté) -->
            <div v-if="!authStore.isAuthenticated" class="auth-tabs">
              <div class="tabs-header">
                <button
                  type="button"
                  @click="activeTab = 'login'"
                  :class="['tab-btn', { 'active': activeTab === 'login' }]"
                >
                  <i class="fas fa-sign-in-alt"></i>
                  Se connecter
                </button>
                <button
                  type="button"
                  @click="activeTab = 'register'"
                  :class="['tab-btn', { 'active': activeTab === 'register' }]"
                >
                  <i class="fas fa-user-plus"></i>
                  Créer un compte
                </button>
              </div>

              <!-- Onglet Connexion -->
              <div v-if="activeTab === 'login'" class="tab-content">
                <form @submit.prevent="handleLogin" class="auth-form">
                  <h2 class="section-title">
                    <i class="fas fa-sign-in-alt"></i>
                    Connexion
                  </h2>

                  <div class="form-group">
                    <label for="login_email" class="form-label required">Email</label>
                    <input
                      id="login_email"
                      v-model="loginForm.email"
                      type="email"
                      class="form-input"
                      :class="{ 'error': authErrors.email }"
                      required
                    />
                    <span v-if="authErrors.email" class="error-message">{{ authErrors.email }}</span>
                  </div>

                  <div class="form-group">
                    <label for="login_password" class="form-label required">Mot de passe</label>
                    <input
                      id="login_password"
                      v-model="loginForm.password"
                      type="password"
                      class="form-input"
                      :class="{ 'error': authErrors.password }"
                      required
                    />
                    <span v-if="authErrors.password" class="error-message">{{ authErrors.password }}</span>
                  </div>

                  <div class="form-group">
                    <label class="checkbox-label">
                      <input
                        v-model="loginForm.remember"
                        type="checkbox"
                        class="form-checkbox"
                      />
                      <span class="checkbox-text">Se souvenir de moi</span>
                    </label>
                  </div>

                  <div class="auth-actions">
                    <button
                      type="submit"
                      class="btn btn-primary btn-block"
                      :disabled="authStore.loading"
                    >
                      <i v-if="authStore.loading" class="fas fa-spinner fa-spin"></i>
                      <i v-else class="fas fa-sign-in-alt"></i>
                      {{ authStore.loading ? 'Connexion...' : 'Se connecter' }}
                    </button>
                  </div>

                  <div v-if="authStore.error" class="error-message">
                    {{ authStore.error }}
                  </div>
                </form>
              </div>

              <!-- Onglet Inscription -->
              <div v-if="activeTab === 'register'" class="tab-content">
                <form @submit.prevent="handleRegister" class="auth-form">
                  <h2 class="section-title">
                    <i class="fas fa-user-plus"></i>
                    Créer un compte
                  </h2>

                  <div class="form-grid">
                    <div class="form-group">
                      <label for="register_firstname" class="form-label required">Prénom</label>
                      <input
                        id="register_firstname"
                        v-model="registerForm.firstname"
                        type="text"
                        class="form-input"
                        :class="{ 'error': authErrors.firstname }"
                        required
                      />
                      <span v-if="authErrors.firstname" class="error-message">{{ authErrors.firstname }}</span>
                    </div>

                    <div class="form-group">
                      <label for="register_lastname" class="form-label required">Nom</label>
                      <input
                        id="register_lastname"
                        v-model="registerForm.lastname"
                        type="text"
                        class="form-input"
                        :class="{ 'error': authErrors.lastname }"
                        required
                      />
                      <span v-if="authErrors.lastname" class="error-message">{{ authErrors.lastname }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="register_company" class="form-label">Entreprise</label>
                    <input
                      id="register_company"
                      v-model="registerForm.company"
                      type="text"
                      class="form-input"
                    />
                  </div>

                  <div class="form-grid">
                    <div class="form-group">
                      <label for="register_email" class="form-label required">Email</label>
                      <input
                        id="register_email"
                        v-model="registerForm.email"
                        type="email"
                        class="form-input"
                        :class="{ 'error': authErrors.email }"
                        required
                      />
                      <span v-if="authErrors.email" class="error-message">{{ authErrors.email }}</span>
                    </div>

                    <div class="form-group">
                      <label for="register_phone" class="form-label required">Téléphone</label>
                      <input
                        id="register_phone"
                        v-model="registerForm.phone"
                        type="tel"
                        class="form-input"
                        :class="{ 'error': authErrors.phone }"
                        required
                      />
                      <span v-if="authErrors.phone" class="error-message">{{ authErrors.phone }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="register_address" class="form-label required">Adresse</label>
                    <input
                      id="register_address"
                      v-model="registerForm.address"
                      type="text"
                      class="form-input"
                      :class="{ 'error': authErrors.address }"
                      required
                    />
                    <span v-if="authErrors.address" class="error-message">{{ authErrors.address }}</span>
                  </div>

                  <div class="form-grid">
                    <div class="form-group">
                      <label for="register_city" class="form-label required">Ville</label>
                      <input
                        id="register_city"
                        v-model="registerForm.city"
                        type="text"
                        class="form-input"
                        :class="{ 'error': authErrors.city }"
                        required
                      />
                      <span v-if="authErrors.city" class="error-message">{{ authErrors.city }}</span>
                    </div>

                    <div class="form-group">
                      <label for="register_postal_code" class="form-label required">Code postal</label>
                      <input
                        id="register_postal_code"
                        v-model="registerForm.postal_code"
                        type="text"
                        class="form-input"
                        :class="{ 'error': authErrors.postal_code }"
                        required
                      />
                      <span v-if="authErrors.postal_code" class="error-message">{{ authErrors.postal_code }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="register_country" class="form-label required">Pays</label>
                    <select
                      id="register_country"
                      v-model="registerForm.country"
                      class="form-select"
                      :class="{ 'error': authErrors.country }"
                      required
                    >
                      <option value="">Sélectionnez un pays</option>
                      <option v-for="country in COUNTRIES" :key="country.code" :value="country.code">
                        {{ country.name }}
                      </option>
                    </select>
                    <span v-if="authErrors.country" class="error-message">{{ authErrors.country }}</span>
                  </div>

                  <div class="form-grid">
                    <div class="form-group">
                      <label for="register_password" class="form-label required">Mot de passe</label>
                      <input
                        id="register_password"
                        v-model="registerForm.password"
                        type="password"
                        class="form-input"
                        :class="{ 'error': authErrors.password }"
                        required
                      />
                      <span v-if="authErrors.password" class="error-message">{{ authErrors.password }}</span>
                    </div>

                    <div class="form-group">
                      <label for="register_password_confirmation" class="form-label required">Confirmer le mot de passe</label>
                      <input
                        id="register_password_confirmation"
                        v-model="registerForm.passwordConfirmation"
                        type="password"
                        class="form-input"
                        :class="{ 'error': authErrors.passwordConfirmation }"
                        required
                      />
                      <span v-if="authErrors.passwordConfirmation" class="error-message">{{ authErrors.passwordConfirmation }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="checkbox-label">
                      <input
                        v-model="registerForm.acceptTerms"
                        type="checkbox"
                        class="form-checkbox"
                        required
                      />
                      <span class="checkbox-text">
                        J'accepte les <a href="/terms" target="_blank">conditions générales</a>
                      </span>
                    </label>
                  </div>

                  <div class="auth-actions">
                    <button
                      type="submit"
                      class="btn btn-primary btn-block"
                      :disabled="authStore.loading || !registerForm.acceptTerms"
                    >
                      <i v-if="authStore.loading" class="fas fa-spinner fa-spin"></i>
                      <i v-else class="fas fa-user-plus"></i>
                      {{ authStore.loading ? 'Création...' : 'Créer mon compte' }}
                    </button>
                  </div>

                  <div v-if="authStore.error" class="error-message">
                    {{ authStore.error }}
                  </div>
                </form>
              </div>
            </div>

            <!-- Statut de connexion et formulaire de commande (si connecté) -->
            <div v-if="authStore.isAuthenticated" class="authenticated-section">
              <!-- Statut de connexion -->
              <div class="auth-status">
                <div class="status-card">
                  <div class="status-info">
                    <i class="fas fa-user-check"></i>
                    <div>
                      <strong>Connecté en tant que {{ authStore.user?.firstName || 'Prénom' }} {{ authStore.user?.lastName || 'Nom' }}</strong>
                      <p>{{ authStore.user?.email }}</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    @click="authStore.logout()"
                    class="btn btn-outline btn-sm"
                  >
                    <i class="fas fa-sign-out-alt"></i>
                    Se déconnecter
                  </button>
                </div>
              </div>

              <!-- Formulaire de commande -->
              <form @submit.prevent="handleSubmit">
              <!-- Informations de facturation automatiques -->
              <div class="form-section">
                <h2 class="section-title">
                  <i class="fas fa-user"></i>
                  Informations de facturation
                </h2>

                <div class="billing-info">
                  <p class="info-text">
                    <i class="fas fa-info-circle"></i>
                    Les informations de facturation seront automatiquement récupérées depuis votre compte.
                  </p>
                </div>
              </div>

              <!-- Méthode de paiement -->
              <div class="form-section">
                <h2 class="section-title">
                  <i class="fas fa-credit-card"></i>
                  Méthode de paiement
                </h2>

                <div class="payment-methods">
                  <div 
                    v-for="method in PAYMENT_METHODS" 
                    :key="method.id"
                    class="payment-method"
                    :class="{ 'selected': form.payment_method === method.id }"
                    @click="form.payment_method = method.id as 'stripe' | 'paypal' | 'bank_transfer'"
                  >
                    <input
                      :id="method.id"
                      v-model="form.payment_method"
                      :value="method.id"
                      type="radio"
                      name="payment_method"
                      class="payment-radio"
                    />
                    <label :for="method.id" class="payment-label">
                      <i :class="method.icon"></i>
                      <div class="payment-info">
                        <div class="payment-name">{{ method.name }}</div>
                        <div class="payment-description">{{ method.description }}</div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Notes -->
              <div class="form-section">
                <h2 class="section-title">
                  <i class="fas fa-comment"></i>
                  Notes (optionnel)
                </h2>
                <textarea
                  v-model="form.notes"
                  class="form-textarea"
                  rows="3"
                  placeholder="Commentaires ou instructions spéciales..."
                ></textarea>
              </div>

              <!-- Conditions -->
              <div class="form-section">
                <div class="form-group">
                  <label class="checkbox-label">
                    <input
                      v-model="form.terms_accepted"
                      type="checkbox"
                      class="form-checkbox"
                      required
                    />
                    <span class="checkbox-text">
                      J'accepte les <a href="/terms" target="_blank">conditions générales de vente</a>
                    </span>
                  </label>
                </div>

                <div class="form-group">
                  <label class="checkbox-label">
                    <input
                      v-model="form.newsletter_subscribe"
                      type="checkbox"
                      class="form-checkbox"
                    />
                    <span class="checkbox-text">
                      Je souhaite recevoir les actualités TechCMS par email
                    </span>
                  </label>
                </div>
              </div>

              <!-- Actions -->
              <div class="form-actions">
                <router-link to="/store/cart" class="btn btn-outline">
                  <i class="fas fa-arrow-left"></i>
                  Retour au panier
                </router-link>
                
                <button
                  type="submit"
                  class="btn btn-primary"
                  :disabled="loading || !form.terms_accepted"
                >
                  <i v-if="loading" class="fas fa-spinner fa-spin"></i>
                  <i v-else class="fas fa-lock"></i>
                  {{ loading ? 'Traitement...' : 'Finaliser la commande' }}
                </button>
              </div>
            </form>
          </div>

          <!-- COLONNE DROITE : Résumé + Sécurité -->
          <div class="checkout-right right-column-forced" style="width: 420px; flex-shrink: 0; max-width: 420px; min-width: 420px; flex-grow: 0;">
            <!-- Résumé de commande -->
            <div class="order-summary">
            <div class="summary-card">
              <h3 class="summary-title">Résumé de la commande</h3>
              
              <!-- Articles -->
              <div class="summary-items">
                <div 
                  v-for="item in cartStore.items" 
                  :key="item.id"
                  class="summary-item"
                >
                  <div class="item-info">
                    <div class="item-name">{{ item.template.name }}</div>
                    <div class="item-quantity">Quantité: {{ item.quantity }}</div>
                  </div>
                  <div class="item-price">{{ formatPrice(item.subtotal) }}</div>
                </div>
              </div>
              
              <!-- Totaux -->
              <div class="summary-totals">
                <div class="summary-line">
                  <span>Sous-total</span>
                  <span>{{ formatPrice(cartStore.subtotal) }}</span>
                </div>
                
                <div class="summary-line">
                  <span>TVA ({{ taxRate }}%)</span>
                  <span>{{ formatPrice(calculatedTax) }}</span>
                </div>
                
                <div class="summary-line total-line">
                  <span>Total</span>
                  <span>{{ formatPrice(cartStore.subtotal + calculatedTax) }}</span>
                </div>
              </div>
            </div>

            <!-- Sécurité -->
            <div class="security-info">
              <div class="security-item">
                <i class="fas fa-shield-alt"></i>
                <div>
                  <strong>Paiement sécurisé</strong>
                  <p>Vos données sont protégées par SSL</p>
                </div>
              </div>
              
              <div class="security-item">
                <i class="fas fa-sync"></i>
                <div>
                  <strong>Activation immédiate</strong>
                  <p>Votre licence sera activée après paiement</p>
                </div>
              </div>
              
              <div class="security-item">
                <i class="fas fa-headset"></i>
                <div>
                  <strong>Support inclus</strong>
                  <p>Support technique avec votre licence</p>
                </div>
              </div>
            </div>
          </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import { useAuthStore } from '@/stores/auth'
import orderService from '@/services/orderService'
import logger from '@/services/logger'
// Plus besoin de CheckoutForm - formulaire simplifié
import { COUNTRIES, PAYMENT_METHODS } from '@/types/order'

// Composables
const router = useRouter()
const cartStore = useCartStore()
const authStore = useAuthStore()

// État
const loading = ref(false)
const errors = ref<Record<string, string>>({})
const authErrors = ref<Record<string, string>>({})
const activeTab = ref<'login' | 'register'>('login')

// Formulaires d'authentification
const loginForm = reactive({
  email: '',
  password: '',
  remember: false
})

const registerForm = reactive({
  firstname: '',
  lastname: '',
  company: '',
  email: '',
  phone: '',
  address: '',
  city: '',
  postal_code: '',
  country: 'FR',
  password: '',
  passwordConfirmation: '',
  acceptTerms: false
})

// Formulaire de commande simplifié (sans billing_address)
const form = reactive({
  payment_method: 'stripe' as 'stripe' | 'paypal' | 'bank_transfer',
  terms_accepted: false,
  newsletter_subscribe: false,
  notes: ''
})

// Computed
const taxRate = computed(() => {
  // Utiliser le pays du client connecté ou France par défaut
  const country = authStore.user?.country || 'FR'
  const rates: Record<string, number> = {
    'FR': 20, 'BE': 21, 'DE': 19, 'ES': 21, 'IT': 22,
    'NL': 21, 'CH': 7.7, 'GB': 20, 'US': 0, 'CA': 5
  }
  return rates[country] || 20
})

const calculatedTax = computed(() => {
  const country = authStore.user?.country || 'FR'
  return orderService.calculateTax(cartStore.subtotal, country)
})

// Méthodes
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const validateForm = (): boolean => {
  errors.value = {}
  
  // Vérifier que l'utilisateur est connecté
  if (!authStore.isAuthenticated || !authStore.user) {
    errors.value.auth = 'Vous devez être connecté pour passer commande'
    return false
  }

  // Vérifier la méthode de paiement
  if (!form.payment_method) {
    errors.value.payment = 'Veuillez sélectionner une méthode de paiement'
  }
  
  if (!form.terms_accepted) {
    errors.value.terms = 'Vous devez accepter les conditions générales'
  }
  
  return Object.keys(errors.value).length === 0
}

// Méthodes d'authentification
const handleLogin = async () => {
  authErrors.value = {}

  if (!loginForm.email || !loginForm.password) {
    authErrors.value.general = 'Veuillez remplir tous les champs'
    return
  }

  try {
    const success = await authStore.login(loginForm.email, loginForm.password, loginForm.remember)

    if (success) {
      logger.info('[CheckoutView] Connexion réussie')

      // Forcer la réactivité de Vue - attendre que le store soit mis à jour
      await new Promise(resolve => setTimeout(resolve, 200))

      // Vérifier que l'authentification est bien effective
      if (authStore.isAuthenticated) {
        // Réinitialiser le formulaire de connexion
        loginForm.email = ''
        loginForm.password = ''
        loginForm.remember = false

        // Afficher un message de succès
        authErrors.value.success = 'Connexion réussie !'
        setTimeout(() => {
          authErrors.value.success = ''
        }, 3000)

        logger.info('[CheckoutView] Interface mise à jour - utilisateur connecté')
      } else {
        // Si le store n'est pas à jour, forcer une vérification
        await authStore.checkAuth()
      }
    }
  } catch (error) {
    logger.error('[CheckoutView] Erreur lors de la connexion', { error })
    authErrors.value.general = 'Erreur lors de la connexion. Veuillez réessayer.'
  }
}

const handleRegister = async () => {
  authErrors.value = {}

  // Validation basique
  if (registerForm.password !== registerForm.passwordConfirmation) {
    authErrors.value.passwordConfirmation = 'Les mots de passe ne correspondent pas'
    return
  }

  if (!registerForm.acceptTerms) {
    authErrors.value.acceptTerms = 'Vous devez accepter les conditions générales'
    return
  }

  const success = await authStore.register(registerForm)

  if (success) {
    logger.info('[CheckoutView] Inscription réussie')
  }
}

// Plus de pré-remplissage automatique - l'utilisateur saisit manuellement

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    // Construire l'adresse de facturation depuis les données du client connecté
    const billing_address = {
      first_name: authStore.user?.firstName || '',
      last_name: authStore.user?.lastName || '',
      company: authStore.user?.company || '',
      email: authStore.user?.email || '',
      phone: authStore.user?.phone || '',
      address_line_1: authStore.user?.address || '',
      address_line_2: '',
      city: authStore.user?.city || '',
      postal_code: authStore.user?.postal_code || '',
      country: authStore.user?.country || 'FR',
      vat_number: ''
    }

    const orderData = {
      items: cartStore.items.map(item => ({
        template_id: item.template.id,
        quantity: item.quantity,
        customizations: item.customizations
      })),
      billing_address,
      payment_method: form.payment_method,
      notes: form.notes
    }
    
    const response = await orderService.createOrder(orderData)
    
    if (response.success && response.order) {
      logger.info('[CheckoutView] Commande créée avec succès', {
        order_id: response.order.id,
        order_number: response.order.order_number
      })
      
      // Vider le panier
      cartStore.clearCart()
      
      // Rediriger vers la page de paiement ou de succès
      if (response.payment_url) {
        window.location.href = response.payment_url
      } else {
        router.push(`/store/order/success/${response.order.order_number}`)
      }
    } else {
      throw new Error(response.message || 'Erreur lors de la création de la commande')
    }
  } catch (error: any) {
    logger.error('[CheckoutView] Erreur lors du checkout', { error: error.message })
    errors.value.general = error.message
  } finally {
    loading.value = false
  }
}

// Plus de watchers pour le pré-remplissage

// Lifecycle
onMounted(() => {
  if (cartStore.isEmpty) {
    router.push('/store/cart')
  }

  // Initialiser le store auth si pas déjà fait
  if (!authStore.initialized) {
    authStore.initialize()
  }

  // Plus de pré-remplissage automatique

  logger.info('[CheckoutView] Page checkout montée')
})
</script>

<style scoped>
/* =================================
   STYLES GLOBAUX POUR OVERRIDE SCOPED
   ================================= */
.checkout-view {
  min-height: 100vh;
  background: var(--bg-primary);
}

.checkout-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem 0;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.checkout-steps {
  display: flex;
  gap: 2rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.step.active {
  color: var(--color-primary);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 0.875rem;
}

.step.active .step-number {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.checkout-content {
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.empty-cart-notice {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.empty-cart-notice i {
  font-size: 4rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* =================================
   LAYOUT HORIZONTAL INFAILLIBLE - FLEXBOX
   ================================= */

/* Container principal avec Flexbox FORCÉ */
.checkout-view .checkout-content .container .checkout-container-horizontal {
  display: flex !important;
  flex-direction: row !important;
  gap: 3rem !important;
  align-items: flex-start !important;
  width: 100% !important;
  max-width: none !important;
  min-height: 600px !important;
  flex-wrap: nowrap !important;
}

/* Colonne gauche - Formulaire */
.checkout-view .checkout-content .container .checkout-container-horizontal .checkout-left {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
  width: auto !important;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Colonne droite - Sidebar */
.checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right {
  width: 420px !important;
  max-width: 420px !important;
  min-width: 420px !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
}

/* Onglets d'authentification */
.auth-tabs {
  margin-bottom: 2rem;
}

.tabs-header {
  display: flex;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 2rem;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
}

.tab-btn:hover {
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

.tab-btn.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: rgba(59, 130, 246, 0.05);
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.auth-form {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.auth-actions {
  margin-top: 1.5rem;
}

/* Statut de connexion */
.authenticated-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.auth-status {
  margin-bottom: 2rem;
}

.status-card {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-info i {
  color: var(--color-success);
  font-size: 1.5rem;
}

.status-info strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.status-info p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.form-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--color-danger);
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--color-danger);
}

.error-message {
  color: var(--color-danger);
  font-size: 0.75rem;
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-method {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method:hover {
  border-color: var(--color-primary);
}

.payment-method.selected {
  border-color: var(--color-primary);
  background: rgba(59, 130, 246, 0.05);
}

.payment-radio {
  display: none;
}

.payment-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
}

.payment-label i {
  font-size: 1.5rem;
  color: var(--color-primary);
  width: 2rem;
  text-align: center;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.payment-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
}

.form-checkbox {
  margin-top: 0.125rem;
}

.checkbox-text {
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.checkbox-text a {
  color: var(--color-primary);
  text-decoration: none;
}

.checkbox-text a:hover {
  text-decoration: underline;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

/* Résumé de commande dans la sidebar */
.checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right .order-summary {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  position: sticky !important;
  top: 2rem !important;
  height: fit-content !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

.summary-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
}

.summary-items {
  margin-bottom: 1.5rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.item-quantity {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.item-price {
  font-weight: 600;
  color: var(--text-primary);
}

.summary-totals {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.total-line {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-primary);
  border-top: 1px solid var(--border-color);
  margin-top: 0.5rem;
  padding-top: 1rem;
}

/* Informations de sécurité dans la sidebar */
.checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right .security-info {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  width: 100% !important;
}

.security-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.security-item:last-child {
  margin-bottom: 0;
}

.security-item i {
  color: var(--color-primary);
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.security-item strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.security-item p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
}

/* =================================
   RESPONSIVE DESIGN INFAILLIBLE
   ================================= */

/* Tablettes */
@media (max-width: 1024px) {
  .checkout-view .checkout-content .container {
    max-width: 1200px !important;
    padding: 0 1.5rem !important;
  }

  .checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right {
    width: 380px !important;
    max-width: 380px !important;
    min-width: 380px !important;
  }
}

/* Mobile - FLEXBOX VERTICAL FORCÉ */
@media (max-width: 768px) {
  .checkout-view .checkout-content .container {
    padding: 0 1rem !important;
  }

  .checkout-view .checkout-content .container .checkout-container-horizontal {
    flex-direction: column !important;
    gap: 2rem !important;
  }

  .checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right {
    width: 100% !important;
    max-width: none !important;
    min-width: auto !important;
    order: 2 !important;
  }

  .checkout-view .checkout-content .container .checkout-container-horizontal .checkout-left {
    order: 1 !important;
    padding: 1.5rem !important;
    width: 100% !important;
  }

  .checkout-view .checkout-content .container .checkout-container-horizontal .checkout-right .order-summary {
    position: static !important;
    margin-bottom: 2rem !important;
  }

  .form-grid {
    grid-template-columns: 1fr !important;
  }

  .form-actions {
    flex-direction: column !important;
  }

  .checkout-steps {
    flex-direction: column !important;
    gap: 1rem !important;
  }
}
</style>

<style>
/* =================================
   STYLES GLOBAUX NON-SCOPED POUR CHECKOUT HORIZONTAL
   ================================= */

/* Container principal avec Flexbox FORCÉ - GLOBAL */
.checkout-container-horizontal,
.horizontal-layout-forced {
  display: flex !important;
  flex-direction: row !important;
  gap: 3rem !important;
  align-items: flex-start !important;
  width: 100% !important;
  max-width: none !important;
  min-height: 600px !important;
  flex-wrap: nowrap !important;
}

/* FORCER ENCORE PLUS AVEC SÉLECTEURS MULTIPLES */
div.checkout-container-horizontal,
div.horizontal-layout-forced,
.checkout-view .checkout-content .container .checkout-container-horizontal,
.checkout-view .checkout-content .container .horizontal-layout-forced {
  display: flex !important;
  flex-direction: row !important;
  gap: 3rem !important;
  align-items: flex-start !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
}

/* Colonne gauche - Formulaire - GLOBAL */
.checkout-left,
.left-column-forced {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
  width: auto !important;
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

/* FORCER COLONNE GAUCHE AVEC SÉLECTEURS MULTIPLES */
div.checkout-left,
div.left-column-forced,
.checkout-container-horizontal .checkout-left,
.horizontal-layout-forced .left-column-forced {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
  width: auto !important;
}

/* Colonne droite - Sidebar - GLOBAL */
.checkout-right {
  width: 420px !important;
  max-width: 420px !important;
  min-width: 420px !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
}

/* Résumé de commande dans la sidebar - GLOBAL */
.checkout-right .order-summary {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  position: sticky !important;
  top: 2rem !important;
  height: fit-content !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
}

/* Informations de sécurité dans la sidebar - GLOBAL */
.checkout-right .security-info {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  width: 100% !important;
}

/* RESPONSIVE DESIGN GLOBAL */
@media (max-width: 1024px) {
  .checkout-right {
    width: 380px !important;
    max-width: 380px !important;
    min-width: 380px !important;
  }
}

@media (max-width: 768px) {
  .checkout-container-horizontal {
    flex-direction: column !important;
    gap: 2rem !important;
  }

  .checkout-right {
    width: 100% !important;
    max-width: none !important;
    min-width: auto !important;
    order: 2 !important;
  }

  .checkout-left {
    order: 1 !important;
    padding: 1.5rem !important;
    width: 100% !important;
  }

  .checkout-right .order-summary {
    position: static !important;
    margin-bottom: 2rem !important;
  }
}
</style>
