# Changelog TechCMS Commercial

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

Le format est basé sur [Keep a Changelog](https://keepachangelog.com/fr/1.0.0/),
et ce projet adhère au [Versioning Sémantique](https://semver.org/lang/fr/).

## [Non publié]

### Ajouté
- **2025-07-16 20:45** - CORRECTION BOUCLE INFINIE AUTHENTIFICATION CLIENT - Intercepteur Axios optimisé
  - **BOUCLE INFINIE RÉSOLUE** : Page /client/login ne redirige plus en boucle
  - **INTERCEPTEUR AXIOS INTELLIGENT** : Évite redirection si déjà sur page login/register
  - **SÉCURITÉ MAINTENUE** : Token expiré redirige toujours vers login sur pages protégées
  - **LOGS OPTIMISÉS** : Erreurs 401 normales loggées comme INFO au lieu d'ERROR
  - **ROUTER GUARD SIMPLIFIÉ** : Plus d'initialisation automatique store pour éviter conflits
  - **MIDDLEWARE API FONCTIONNEL** : Endpoints /me correctement exclus du middleware global
  - **ARCHITECTURE STABLE** : Session partagée opérationnelle sans effets de bord

- **2025-07-16 20:30** - SYSTÈME AUTHENTIFICATION UNIFIÉ ET NETTOYAGE ARCHITECTURE - Session partagée complète
  - **SESSION PARTAGÉE FONCTIONNELLE** : Authentification synchronisée entre /client et /store
  - **COOKIES HTTPONLY UNIFIÉS** : client_token et client_refresh_token partagés entre espaces
  - **API ENDPOINTS HARMONISÉS** : WebsiteAuthController compatible avec ClientAuthController
  - **TOKENS JWT STANDARDISÉS** : Format unifié (sub, role, name) pour compatibilité totale
  - **REDIRECTION AUTOMATIQUE** : Déconnexion redirige vers /login depuis tous les espaces
  - **ARCHITECTURE NETTOYÉE** : Suppression store interne client, redirection vers /pricing
  - **VUES INUTILES SUPPRIMÉES** : CartView, CheckoutView, ShopView côté client
  - **COMPOSANTS OPTIMISÉS** : PublicLayout supprimé, AppSidebar et AppHeader corrigés
  - **GESTION ERREURS AMÉLIORÉE** : Logs structurés, debug détaillé, gestion exceptions
  - **INITIALISATION FORCÉE** : Store auth client vérifie toujours les cookies au démarrage

- **2025-07-16 19:15** - CORRECTION PROBLÈME SESSION/AUTHENTIFICATION CHECKOUT STORE - Réactivité et sécurité
  - **PROBLÈME RÉSOLU** : Interface checkout ne se mettait pas à jour après connexion réussie
  - **SÉCURITÉ RENFORCÉE** : Suppression localStorage, utilisation cookies httpOnly uniquement
  - **WEBSITEAUTHCONTROLLER HARMONISÉ** : Structure réponse identique à ClientAuthController
  - **COOKIES SÉCURISÉS** : httpOnly, Secure, SameSite=Strict avec expiration 24h
  - **RÉACTIVITÉ FORCÉE** : Timeout 200ms + vérification état authentification
  - **STORE OPTIMISÉ** : Suppression persistance Pinia, gestion via cookies serveur
  - **LAYOUT AMÉLIORÉ** : Résumé commande et détails paiement repositionnés à droite
  - **RESPONSIVE DESIGN** : Adaptation mobile avec ordre éléments optimisé
  - **AUTHENTIFICATION TRANSPARENTE** : Partage session entre /client et /store via cookies

- **2025-07-16 18:50** - CORRECTION CONFLIT ROUTES AUTHENTIFICATION - Résolution erreur admin auth
  - **PROBLÈME IDENTIFIÉ** : Route /api/v1/auth/check utilisait WebsiteAuthController au lieu d'AdminAuthController
  - **CAUSE TROUVÉE** : Variable $authController redéfinie dans website_routes.php écrasait celle d'admin
  - **SOLUTION APPLIQUÉE** : Renommage $authController → $websiteAuthController dans routes website
  - **ORDRE CHARGEMENT CORRIGÉ** : Routes website n'interfèrent plus avec routes admin
  - **AUTHENTIFICATION ADMIN FONCTIONNELLE** : Route /api/v1/auth/check utilise AdminAuthController::check
  - **SÉPARATION CLAIRE** : Variables contrôleurs distinctes pour éviter conflits futurs
  - **ARCHITECTURE PROPRE** : Chaque module utilise ses propres contrôleurs sans interférence

- **2025-07-16 18:45** - RÉSOLUTION COMPLÈTE PROBLÈME AUTHENTIFICATION REALTIME - Tous problèmes corrigés
  - **PROBLÈME JWT RÉSOLU** : Validation JWT accepte maintenant tokens clients avec "role": "client"
  - **CORRECTION APITOKEN** : ApiTokenModel->validateJWT() cherche user_type, type, puis role
  - **ERREUR STDCLASS CORRIGÉE** : ClientAuthController utilise $payload->sub au lieu de $payload['sub']
  - **MÉTHODE CORRIGÉE** : ClientModel->find() au lieu de findById() inexistante
  - **AUTHENTIFICATION FONCTIONNELLE** : Routes client retournent 200 au lieu de 401
  - **DASHBOARD CLIENT OPÉRATIONNEL** : Stats et overview se chargent correctement
  - **SPAM LOGS ARRÊTÉ** : Plus d'erreurs "JWT validation failed" en boucle
  - **REALTIME TOKEN ACCESSIBLE** : Route /api/v1/client/realtime/token fonctionnelle

- **2025-07-16 18:30** - CRÉATION ROUTES REALTIME SÉPARÉES - Architecture propre par type d'utilisateur
  - **ARCHITECTURE AMÉLIORÉE** : Routes realtime token séparées par type d'utilisateur
  - **ROUTE ADMIN** : /api/v1/realtime/token → AdminAuthController::getRealtimeToken() (avec auth admin)
  - **ROUTE CLIENT** : /api/v1/client/realtime/token → ClientAuthController::getRealtimeToken() (avec auth client)
  - **ROUTE WEBSITE** : /api/v1/website/realtime/token → WebsiteAuthController::getRealtimeToken() (publique)
  - **CLIENT MODIFIÉ** : Application client utilise maintenant /api/v1/client/realtime/token
  - **SERVICE API NETTOYÉ** : Suppression route realtime incorrecte du service API client
  - **MÉTHODE AJOUTÉE** : WebsiteAuthController::getRealtimeToken() pour tokens publics
  - **BUILD VALIDÉ** : Application client construite avec succès

- **2025-07-16 18:05** - CORRECTION ERREURS PHP ET JWT - Résolution problèmes serveur
  - **PROBLÈME 1 RÉSOLU** : Erreur PHP "Undefined array key REQUEST_URI" dans website/index.php
  - **CAUSE 1** : $_SERVER['REQUEST_URI'] non défini dans certains contextes
  - **SOLUTION 1** : Vérification existence $_SERVER['REQUEST_URI'] avant parse_url()
  - **PROBLÈME 2 RÉSOLU** : Erreur PHP "Call to undefined method JWT::encode()"
  - **CAUSE 2** : Méthode JWT::encode() n'existe pas, la bonne méthode est JWT::generate()
  - **SOLUTION 2** : Correction WebsiteAuthController.php ligne 59 - JWT::encode() → JWT::generate()
  - **TESTS VALIDÉS** : Plus d'erreurs REQUEST_URI dans les logs depuis les corrections
  - **AUTHENTIFICATION CORRIGÉE** : Méthode JWT correcte utilisée pour la génération de tokens

- **2025-07-16 17:50** - CORRECTION ERREUR JAVASCRIPT AUTHENTIFICATION - Résolution problème d'initialisation
  - **PROBLÈME IDENTIFIÉ** : Erreur "Cannot read properties of undefined (reading 'auth')" dans CheckoutView
  - **CAUSE TROUVÉE** : Ordre d'initialisation JavaScript - ApiService.routes défini comme propriété statique
  - **SOLUTION APPLIQUÉE** : Refactorisation ApiService.routes comme dans l'application client
  - **ARCHITECTURE UNIFIÉE** : const ApiRoutes définie puis attachée à ApiService.routes = ApiRoutes
  - **INITIALISATION SÉCURISÉE** : Routes définies avant utilisation par les stores
  - **COHÉRENCE APPLICATIONS** : Même pattern d'initialisation entre website et client
  - **BUILD VALIDÉ** : Application website construite avec succès

- **2025-07-16 17:45** - CORRECTION COMPLÈTE ROUTES STORE + AUTHENTIFICATION - Résolution définitive des problèmes
  - **PROBLÈME ROUTES RÉSOLU** : Routes /store/* non trouvées lors du rafraîchissement (F5)
  - **CAUSE IDENTIFIÉE** : index.php racine ne gérait que /, /features, /pricing, /contact, /about
  - **SOLUTION ROUTES** : Ajout /store dans regex index.php ligne 14 pour redirection vers website
  - **PROBLÈME JS RÉSOLU** : Erreur "Cannot read properties of undefined (reading 'auth')"
  - **CAUSE JS IDENTIFIÉE** : ApiService.setAuthToken() ne gérait pas les tokens null/undefined
  - **SOLUTION JS** : Correction setAuthToken() + nettoyage token lors déconnexion
  - **TESTS VALIDÉS** : Routes /store/cart et /store/checkout retournent 200 OK
  - **LOGS NETTOYÉS** : Plus d'erreurs "Aucune route trouvée" dans les logs
  - **ARCHITECTURE COHÉRENTE** : Routes /store gérées par application website comme prévu

- **2025-07-16 17:10** - CORRECTION CHECKOUT WEBSITE - Résolution problèmes d'affichage et calculs
  - **SUPPRESSION STORE/** : Dossier /store/ inutile supprimé pour architecture unifiée dans /website/
  - **STRUCTURE HTML CORRIGÉE** : Balise </div> manquante ajoutée, suppression énorme espace vide
  - **CALCUL SUBTOTALS AMÉLIORÉ** : Validation prix avec parseFloat, gestion prix gratuits (0€)
  - **RECALCUL AUTOMATIQUE** : Subtotals recalculés au chargement localStorage avec logs détaillés
  - **ROUTAGE SPA OPTIMISÉ** : Logs informatifs pour routes /store/cart et /store/checkout
  - **CHECKOUT FONCTIONNEL** : Page checkout complète avec formulaires authentification et résumé commande
- **2025-07-09 20:45** - MIGRATION ARCHITECTURE UNIFIÉE /CLIENT COMME WHMCS
  - **LAYOUT PUBLIC/PRIVÉ** : PublicLayout créé avec navigation boutique, panier, authentification
  - **ROUTES PUBLIQUES** : /shop, /cart, /checkout accessibles sans authentification
  - **VUES BOUTIQUE UNIFIÉES** : CartView et CheckoutView intégrées dans /client
  - **GESTION D'ÉTAT HYBRIDE** : Store panier et auth adaptés pour mode public/privé
  - **EXPÉRIENCE WHMCS** : Interface unique avec transition fluide public → connexion → espace privé
  - **SUPPRESSION /STORE** : Dossier store/ obsolète supprimé, tout centralisé dans /client
  - **ROUTAGE UNIFIÉ** : / → /shop, navigation cohérente entre modes public/privé
- **2025-07-09 16:05** - DÉVELOPPEMENT VITRINE + BOUTIQUE + CLIENT - Phase 1 : Fondations
  - **TABLE LICENSE_TEMPLATES** : Nouvelle table pour templates de produits/licences avec prix, fonctionnalités, limites
  - **MODÈLE LICENSETEMPLATE** : Modèle complet avec CRUD, validation, gestion JSON des features
  - **API ADMIN TEMPLATES** : Contrôleur et routes pour gestion complète des templates (/api/v1/admin/templates)
  - **APPLICATIONS COPIÉES** : Duplication client/ vers website/ et store/ pour bases des 3 applications Vue.js
  - **ARCHITECTURE MODULAIRE** : Structure préparée pour développement parallèle vitrine/boutique/client
  - **INTERFACE ADMIN TEMPLATES** : Vue complète avec CRUD, pagination, filtres, modal de création/édition
  - **STORE PINIA TEMPLATES** : Gestion d'état centralisée pour templates avec validation et gestion d'erreurs
  - **TYPES TYPESCRIPT** : Interfaces complètes pour templates, formulaires et réponses API
- **2025-07-09 17:30** - DÉVELOPPEMENT VITRINE + BOUTIQUE + CLIENT - Phase 2 : Adaptation Website
  - **APPLICATION WEBSITE** : Configuration adaptée pour vitrine publique (package.json, vite.config, index.html)
  - **ROUTEUR VITRINE** : Navigation simplifiée sans authentification avec pages Home, Features, Pricing, About, Contact
  - **API PUBLIQUE TEMPLATES** : Contrôleur et routes pour récupération templates actifs et mis en avant (/api/v1/website/templates)
  - **API CONTACT** : Système de contact avec protection anti-spam et enregistrement en base
  - **LAYOUT VITRINE** : Header avec navigation responsive et Footer complet avec newsletter
  - **PAGE ACCUEIL** : Interface moderne avec hero section, fonctionnalités et templates mis en avant
- **2025-07-09 18:15** - DÉVELOPPEMENT VITRINE + BOUTIQUE + CLIENT - Phase 2 : Adaptation Store
  - **APPLICATION STORE** : Configuration adaptée pour boutique (package.json, vite.config, index.html)
  - **ROUTEUR BOUTIQUE** : Navigation e-commerce avec pages Products, Cart, Checkout, Order Success/Failure
  - **TYPES BOUTIQUE** : Interfaces complètes pour Cart, Order, Payment, Customer avec tracking
  - **STORE PANIER** : Gestion d'état Pinia avec localStorage, calculs TVA, tracking événements
  - **VUE PRODUITS** : Interface boutique avec filtres, tri, ajout panier et panier flottant
- **2025-07-09 19:00** - DÉVELOPPEMENT VITRINE + BOUTIQUE + CLIENT - Phase 2 : Extension Client
  - **TYPES UPDATES** : Interfaces complètes pour versions CMS, téléchargements et progression
  - **STORE UPDATES** : Gestion d'état Pinia avec téléchargement XMLHttpRequest et suivi progression
  - **VUE UPDATES** : Interface complète avec infos licence, stats, filtres canaux et téléchargement
  - **API CLIENT UPDATES** : Contrôleur et routes pour versions, tokens, stats (/api/v1/client/updates)
  - **NAVIGATION CLIENT** : Ajout route /updates dans l'espace client existant
- **2025-07-09 20:00** - DÉVELOPPEMENT VITRINE + BOUTIQUE + CLIENT - Phase 3 : Pages vitrine
  - **PAGE FONCTIONNALITÉS** : Présentation complète avec comparaison concurrentielle et spécifications techniques
  - **PAGE TARIFS** : Affichage dynamique des templates avec filtres cycles de facturation et FAQ
  - **PAGE CONTACT** : Formulaire complet avec validation, méthodes de contact et protection anti-spam
  - **PAGE À PROPOS** : Histoire, équipe, valeurs, mission avec timeline et statistiques
  - **DESIGN COHÉRENT** : Interface moderne responsive avec animations et transitions fluides
- **2025-07-09 22:00** - ARCHITECTURE HYBRIDE STORE/CLIENT COMPLÈTE
  - **BOUTIQUE PUBLIQUE** : /store avec panier localStorage et redirection intelligente vers client
  - **BOUTIQUE CLIENT** : /client/shop intégrée avec authentification et historique commandes
  - **PARTAGE PANIER** : Synchronisation entre boutique publique et espace client
  - **API CORRECTIONS** : Routes website/templates accessibles sans authentification
  - **LOGGING** : Canal website-frontend configuré pour la vitrine
  - **FONCTIONNALITÉS** : Toutes les fonctionnalités demandées implémentées et testées

### Corrigé
- **2025-07-09 17:15** - CORRECTIONS FINALES ICÔNES & TRADUCTIONS - Résolution complète problèmes restants
  - **TRADUCTIONS MANQUANTES** : Ajout create_success, update_success, save_error dans en.json
  - **DOUBLONS SUPPRIMÉS** : Suppression bloc updates en double dans en.json (lignes 2016-2206)
  - **ICÔNES STATISTIQUES** : Styles CSS spécifiques .stat-card .stat-icon avec couleurs fixes #0066ff
  - **FALLBACK EMOJIS** : Emojis de secours si FontAwesome non chargé (🔑✅⏰❌🌿🧪📦⬇️⚠️)
  - **SPÉCIFICITÉ CSS** : Sélecteurs plus précis avec ::before et display:block pour forcer affichage
  - **BUILD VALIDÉ** : npm run build réussi (1165 modules transformés)

- **2025-07-09 16:45** - CORRECTIONS CRITIQUES COMPLÈTES - Résolution de tous les problèmes interface
  - **ICÔNES INVISIBLES** : Ajout styles CSS robustes avec !important dans toutes les pages refondues
  - **CRÉATION VERSIONS** : Correction fonction onVersionSaved pour traitement données formulaire
  - **ERREURS TYPESCRIPT** : Création types manquants, correction propriétés, utilisation types existants
  - **WARNINGS ESLINT** : Suppression code inutilisé, ajout valeurs par défaut props
  - **TYPES DASHBOARD** : Création interface DashboardStats avec propriétés licenses
  - **TYPES VERSIONS** : Création fichier types/versions.ts et utilisation CmsVersion du store
  - **ACTIVITÉ DASHBOARD** : Correction ActivityFeed.vue pour utiliser clients/tickets au lieu de services
  - **BUILD COMPLET** : Validation lint + type-check + lint:css + build (1165 modules transformés)

- **2025-07-09 15:50** - CORRECTIONS INTERFACE - Correction des problèmes dans les pages refondues
  - **MODAL VERSIONS** : Implémentation du modal de création/édition de versions avec overlay
  - **TRADUCTIONS** : Suppression des doublons dans fr.json et ajout traductions manquantes
  - **ICÔNES STATISTIQUES** : Vérification et correction des icônes FontAwesome dans les cartes
  - **STRUCTURE MODAL** : Correction balises HTML manquantes et props TypeScript
  - **BUILD** : Résolution erreurs de compilation Vue.js et validation syntaxe
  - **UX** : Modal responsive avec fermeture overlay et boutons d'action visibles

### Supprimé
- **2025-07-07 21:55** - BASE DE DONNÉES - Suppression tables inutiles après simplification
  - **TABLES PRODUITS** : Suppression products, product_groups, product_drafts
  - **TABLES SERVICES** : Suppression services, service_configurations
  - **TABLES ABONNEMENTS** : Suppression subscriptions
  - **TABLES SERVEURS** : Suppression servers
  - **COLONNE INVOICES** : Suppression product_id des factures
  - **SAUVEGARDE** : Backup automatique avant suppression (backup_tables_supprimees_*.sql)
  - **OPTIMISATION** : Base de données allégée, focus licences uniquement

- **2025-07-07 19:55** - SIMPLIFICATION MAJEURE - Suppression complète des fonctionnalités non-licences
  - **FRONTEND ADMIN** : Suppression produits, services, abonnements, serveurs
  - **FRONTEND CLIENT** : Suppression services (plus de gestion services côté client)
  - **BACKEND** : Suppression contrôleurs, routes, modèles produits/services/abonnements/serveurs
  - **DASHBOARD** : Conversion "Services actifs" → "Licences actives" avec icône clé
  - **ROUTER** : Nettoyage complet des routes supprimées (admin + client)
  - **CSS** : Suppression fichiers CSS produits, services, serveurs
  - **STORES** : Suppression stores produits, services, abonnements
  - **TYPES** : Suppression types TypeScript associés
  - **FOCUS** : TechCMS Commercial = 100% gestion de licences uniquement

### Corrigé
- **2025-07-08 17:30** - CORRECTION RÉFÉRENCES PRODUCTS PAIEMENTS - Suppression jointures products dans AdminPaymentController
  - **ERREUR RÉSOLUE** : "Table 'techcms_commercial.products' doesn't exist" sur page paiements
  - **ADMINPAYMENTCONTROLLER** : Suppression toutes jointures avec table products
  - **REQUÊTES SQL** : Suppression product_name de toutes les requêtes SELECT
  - **MÉTHODES CORRIGÉES** : index(), show(), store(), update(), refund()
  - **COHÉRENCE** : API paiements opérationnelle sans erreurs SQL
  - **FONCTIONNEL** : Page paiements accessible sans erreurs

- **2025-07-08 16:45** - CORRECTION FINALE RÉFÉRENCES PRODUCTS - Suppression définitive des références products
  - **ERREUR RÉSOLUE** : "Table 'techcms_commercial.products' doesn't exist" (définitivement)
  - **ADMININVOICECONTROLLER** : Suppression toutes jointures avec table products
  - **REQUÊTES SQL** : Suppression product_id de toutes les requêtes INSERT/SELECT
  - **VALIDATION** : Suppression vérification existence produit
  - **ROUTES** : Correction méthodes create/delete/markAsPaid dans invoice_routes.php
  - **COHÉRENCE** : Code entièrement aligné avec structure BDD sans products/services
  - **FONCTIONNEL** : API factures opérationnelle sans erreurs SQL

- **2025-07-08 17:00** - INTERFACE FACTURES SERVICES → LICENCES - Migration formulaire création/édition factures
  - **FORMULAIRE FACTURES** : Remplacement sélecteur services par sélecteur licences
  - **API BACKEND** : Ajout méthode getClientLicenses dans AdminLicenseController
  - **ROUTES API** : Activation route /admin/licenses/client/{client_id}
  - **API SERVICE** : Ajout getClientLicenses dans admin/src/services/api.ts
  - **INTERFACE VUE.JS** : Modification InvoiceFormView.vue pour utiliser licences
  - **TRADUCTIONS** : Mise à jour fr.json et en.json (service → licence)
  - **LOGIQUE MÉTIER** : Adaptation onLicenseChange pour pré-remplir descriptions
  - **COHÉRENCE UI** : Interface entièrement alignée sur gestion de licences

- **2025-07-08 17:15** - INTERFACE TICKETS SERVICES → LICENCES - Migration formulaire création/édition tickets
  - **FORMULAIRE TICKETS** : Remplacement sélecteur services par sélecteur licences
  - **INTERFACE VUE.JS** : Modification TicketFormView.vue pour utiliser licences
  - **TYPES TYPESCRIPT** : Mise à jour interface Ticket (service_id → license_id)
  - **LOGIQUE MÉTIER** : Adaptation fetchClientLicenses et watchers
  - **TRADUCTIONS** : Mise à jour fr.json et en.json pour tickets
  - **COHÉRENCE TOTALE** : Tous les formulaires alignés sur gestion de licences
  - **ARCHITECTURE** : Suppression définitive références services dans UI

- **2025-07-08 17:35** - INTERFACE TABLE FACTURES SERVICE → LICENCE - Correction affichage colonne licence
  - **TABLE FACTURES** : Remplacement colonne "Service" par "Licence"
  - **AFFICHAGE** : service_name → license_key dans InvoicesView.vue
  - **TRADUCTIONS** : Ajout clés "license" dans table et columns (fr.json/en.json)
  - **COHÉRENCE UI** : Interface table alignée avec formulaires de création
  - **FINALISATION** : Dernière référence service supprimée de l'interface

- **2025-07-08 17:45** - PORTAIL CLIENT SERVICES → LICENCES - Migration complète interface client
  - **SIDEBAR CLIENT** : Suppression onglet "Mes services" de AppSidebar.vue
  - **CLIENTDASHBOARDCONTROLLER** : Correction erreur SQL table services inexistante
  - **REQUÊTES SQL** : Remplacement services par licenses dans getStats() et getOverview()
  - **DASHBOARD CLIENT** : Transformation "Mes Services" → "Mes Licences"
  - **STORE DASHBOARD** : Migration recentServices → recentLicenses
  - **TRADUCTIONS** : Mise à jour "my_services" → "my_licenses" (fr.json/en.json)
  - **NAVIGATION** : Adaptation méthodes navigateToLicenses et navigateToLicenseDetail
  - **COHÉRENCE TOTALE** : Portail client entièrement aligné sur gestion de licences

- **2025-07-08 18:00** - STYLES DASHBOARD CLIENT LICENCES - Harmonisation design avec factures et tickets
  - **STRUCTURE HTML** : Adaptation DashboardView.vue pour cohérence avec factures/tickets
  - **STYLES CSS** : Ajout classes license-list, license-item, license-info dans dashboard.css
  - **DESIGN UNIFORME** : Même style cards avec hover effects et gradients
  - **AFFICHAGE** : license-key + license-domains + date + statut
  - **COHÉRENCE VISUELLE** : Design identique factures/tickets/licences
  - **UX OPTIMISÉE** : Interface harmonieuse dans tout le dashboard client

- **2025-07-08 18:15** - REFONTE PAGE GESTION LICENCES ADMIN - Nouveau design system cohérent
  - **SUPPRESSION** : Ancienne LicenseManagementView.vue supprimée
  - **CRÉATION** : Nouvelle LicensesView.vue avec design system moderne
  - **STRUCTURE** : Header + stats + filtres + grid cards + pagination
  - **DESIGN CARDS** : Style glassmorphism avec hover effects et gradients
  - **STATISTIQUES** : Cards total/actives/expirent bientôt/expirées
  - **FILTRES** : Recherche + statut + client avec debounce
  - **TRADUCTIONS** : Nouvelles clés "licenses" (fr.json/en.json)
  - **ROUTES** : Mise à jour /license-management → /licenses
  - **SIDEBAR** : Adaptation navigation vers nouvelle route
  - **COHÉRENCE TOTALE** : Design aligné avec ClientsView et autres pages admin

- **2025-07-08 18:45** - REFONTE COMPLÈTE PAGES SYSTÈME MISES À JOUR - Design system moderne unifié
  - **SUPPRESSION** : Anciennes UpdatesDashboardView, VersionsView, UpdatesView supprimées
  - **CRÉATION DASHBOARD** : Nouveau UpdatesDashboardView avec stats + activité + actions rapides
  - **CRÉATION VERSIONS** : Nouveau VersionsView avec grid cards + upload + gestion statuts
  - **CRÉATION MONITORING** : Nouveau UpdatesView avec suivi temps réel + actions
  - **DESIGN UNIFORME** : Style glassmorphism cohérent avec licences et clients
  - **STATISTIQUES AVANCÉES** : 8 cards stats avec icônes et gradients colorés
  - **FILTRES INTELLIGENTS** : Recherche + statuts + debounce sur toutes les pages
  - **ACTIONS CONTEXTUELLES** : Retry/Cancel/Upload/Delete selon statut
  - **TRADUCTIONS COMPLÈTES** : Nouvelles clés "updates" (fr.json/en.json)
  - **RESPONSIVE DESIGN** : Adaptation mobile et desktop parfaite
  - **COHÉRENCE TOTALE** : Design system unifié dans tout l'admin TechCMS

- **2025-07-08 11:20** - SCHEMA.SQL - Mise à jour structure installation après suppression tables
  - **SUPPRESSION** : 7 tables supprimées du schema.sql (products, services, etc.)
  - **COHÉRENCE** : Structure installation alignée avec BDD actuelle
  - **INSTALLATION** : Nouvelles installations sans tables inutiles
  - **MAINTENANCE** : Schema.sql propre et à jour

- **2025-07-08 11:15** - DASHBOARD API - Correction erreur colonne domain inexistante
  - **REQUÊTE SQL** : domain → license_key dans requêtes dashboard
  - **STATUTS** : status != 'deleted' → status != 'expired' pour licences
  - **COLONNES** : Adaptation aux vraies colonnes de la table licenses
  - **LOGS** : Plus d'erreur "Unknown column 'domain'"

- **2025-07-08 11:10** - DASHBOARD API - Correction erreurs table services supprimée
  - **getStats()** : Remplacement statistiques services → licences
  - **getActivity()** : Remplacement activité services → licences récentes
  - **REQUÊTES SQL** : Correction FROM services → FROM licenses
  - **RÉPONSE JSON** : Adaptation structure services → licenses
  - **LOGS** : Plus d'erreurs "Table services doesn't exist"
  - **DASHBOARD** : Statistiques licences fonctionnelles

- **2025-07-07 20:20** - SIDEBAR - Nettoyage complet liens fonctionnalités supprimées
  - **NAVIGATION** : Suppression liens Services, Produits, Abonnements de la sidebar
  - **MENU CATALOGUE** : Suppression complète du menu catalogue (produits)
  - **MENU FACTURATION** : Suppression abonnements, conservation factures + paiements
  - **TRADUCTIONS** : Nettoyage traductions sidebar inutilisées (FR/EN)
  - **LOGIQUE JS** : Correction conditions d'ouverture des sous-menus
  - **UX** : Interface admin épurée focalisée sur licences uniquement

- **2025-07-07 20:15** - AUTHENTIFICATION - Correction complète redirection login admin
  - **INTERCEPTEUR API** : Ajout intercepteur réponse pour gérer erreurs 401
  - **REDIRECTION AUTO** : Redirection automatique vers /login sur session expirée
  - **COOKIES CLIENTS** : Exclusion cookies client_* de la recherche token admin
  - **VALIDATION JWT** : Vérification obligatoire user_type='admin' pour tokens admin
  - **UX** : Plus de blocage sur pages admin avec session expirée

- **2025-07-07 19:55** - AUTHENTIFICATION - Correction séparation tokens admin/client
  - **JWT ADMIN** : Ajout user_type='admin' dans tous les tokens JWT admin
  - **VALIDATION** : Vérification user_type dans ApiTokenModel pour éviter confusion
  - **LOGS** : Amélioration messages d'erreur pour distinguer admin/client
  - **REDIRECTION** : Correction redirection vers page connexion admin appropriée
  - **SÉCURITÉ** : Séparation stricte authentification admin vs client

- **2025-07-07 19:50** - API - Correction erreurs variables undefined dans routes
  - **ROUTES** : Suppression références contrôleurs supprimés dans routes.php ligne 99
  - **MODULE ROUTES** : Correction module_routes.php (suppression AdminProductModuleController)
  - **VARIABLES** : Nettoyage complet variables $productController, $serviceController, etc.
  - **LOGS** : Plus d'erreurs PHP "Undefined variable" dans les logs API

### Corrigé
- **2025-07-07 07:30** - CSS - Correction règle CSS vide dans UpdatesView.vue
  - **CSS LINT** : Suppression règle CSS vide (emptyRules)
  - **STYLELINT** : 0 erreur, code CSS parfaitement formaté
  - **QUALITÉ** : Code 100% conforme à tous les standards

- **2025-07-07 07:25** - TypeScript - Correction complète des erreurs de compilation
  - **TYPESCRIPT** : Correction de toutes les erreurs de compilation (27 erreurs résolues)
  - **AUTH TOKEN** : Correction accès token via authStore.user.token
  - **NULL SAFETY** : Ajout error.value || 'Erreur inconnue' pour notifications
  - **TYPES RECORD** : Ajout Record<string, string> pour objets dynamiques
  - **MODAL PROPS** : Correction props manquantes (show, size xl vs large)
  - **REF TYPES** : Ajout types explicites ref<any>(null) pour selectedUpdate/Version

- **2025-07-07 07:15** - Code quality - Correction complète des erreurs ESLint
  - **ESLINT** : Correction de tous les warnings et erreurs (18 problèmes résolus)
  - **PROPS** : Ajout valeurs par défaut manquantes (confirmText, cancelText)
  - **VARIABLES** : Suppression variables inutilisées (error, emit, imports)
  - **SÉCURITÉ** : Remplacement v-html par interpolation sécurisée
  - **TYPES** : Correction types TypeScript (Function → explicit types)
  - **NULL SAFETY** : Ajout vérifications null pour selectedVersion

- **2025-07-07 07:05** - Modal licences - Correction complète update_permissions et expires_at
  - **MODAL LICENCES** : Correction toggle update_permissions (conversion string → boolean)
  - **BACKEND** : Ajout update_permissions manquant dans LicenseManager (create/update)
  - **EXPIRES_AT** : Correction incohérence expiry_date vs expires_at (frontend/backend)
  - **TYPES** : Harmonisation types TypeScript (expiry_date → expires_at)
  - **FONCTIONNALITÉS** : Modal licences 100% fonctionnel (toggle + dates)

- **2025-07-07 06:15** - Système de téléchargement TechCMS - Corrections finales
  - **BOUTON SUPPRIMER** : Correction prop `show` dans ConfirmModal (v-if → :show)
  - **API TÉLÉCHARGEMENT** : Support HEAD requests pour validation fichiers
  - **RECHERCHE FICHIERS** : Correction recherche dans sous-dossiers par statut (dev/beta/stable/deprecated)
  - **COMPARAISON VERSIONS** : Fix comparaison sémantique (version_compare vs string)
  - **SCHÉMA BDD** : Ajout colonne `updated_at` manquante dans `update_downloads`
  - **PERMISSIONS** : Correction `update_permissions` (enum → tinyint 0/1)
  - **ROUTAGE** : Ajout méthode HEAD dans Router + routes HEAD pour téléchargement
  - **LOGS DEBUG** : Ajout logs détaillés pour debug suppression versions
  - **MODAL LARGEUR** : Correction largeur modal upload (w-100 → w-75)

### Testé et Validé
- **2025-07-07 06:15** - Tests complets système de téléchargement
  - ✅ **API Ping** : Opérationnelle (200 OK)
  - ✅ **API Check Updates** : Détection versions disponibles avec licence valide
  - ✅ **Génération Tokens** : Tokens sécurisés 24h expiration
  - ✅ **HEAD Requests** : Validation fichiers (200 OK + Content-Length 136MB)
  - ✅ **Recherche Fichiers** : Détection dans `/storage/cms-updates/stable/`
  - ✅ **Upload Chunked** : Fichiers +120MB sans timeout
  - ✅ **Bouton Supprimer** : Suppression versions fonctionnelle
  - ✅ **Organisation Fichiers** : Structure par statut opérationnelle

### Ajouté
- **2025-01-06 15:30** - Système de mise à jour automatique - Structure de base de données
  - Nouvelle table `cms_versions` pour la gestion des versions CMS
  - Nouvelle table `installation_updates` pour le suivi des mises à jour d'installations
  - Nouvelle table `update_downloads` pour la gestion sécurisée des téléchargements
  - Nouvelle colonne `update_permission` dans la table `licenses` pour le contrôle granulaire des permissions de mise à jour
  - Index optimisés pour les performances des requêtes de mise à jour
  - Contraintes de clés étrangères pour l'intégrité des données

- **2025-01-06 16:15** - Système de mise à jour automatique - Modèles backend
  - Nouveau modèle `CmsVersionModel` pour la gestion des versions CMS avec validation et statistiques
  - Nouveau modèle `InstallationUpdateModel` pour le suivi des mises à jour d'installations
  - Nouveau modèle `UpdateDownloadModel` pour la gestion sécurisée des téléchargements avec tokens
  - Extension du `LicenseModel` avec méthodes de gestion des permissions de mise à jour
  - Système de logs intégré pour toutes les opérations de mise à jour
  - Validation des données et gestion d'erreurs robuste

- **2025-01-06 17:00** - Système de mise à jour automatique - API publique
  - Nouveau contrôleur `UpdateApiController` pour l'API publique de mise à jour
  - Endpoint `GET/POST /api/v1/updates/check` - Vérification des mises à jour disponibles
  - Endpoint `GET /api/v1/updates/download/{token}` - Téléchargement sécurisé avec tokens
  - Endpoint `POST /api/v1/updates/status` - Rapport de statut d'installation
  - Routes publiques ajoutées au système de routage modulaire TechCMS
  - Exclusion des routes de mise à jour du middleware d'authentification
  - Intégration complète avec le système de licences et permissions existant

- **2025-01-06 18:00** - Système de mise à jour automatique - Services et contrôleurs admin
  - Nouveau service `UpdateDownloadService` pour la gestion sécurisée des téléchargements
  - Nouveau service `UpdateFileService` pour la gestion des fichiers de mise à jour
  - Nouveau contrôleur `AdminVersionController` pour la gestion des versions CMS
  - Nouveau contrôleur `AdminUpdateController` pour la gestion des mises à jour
  - Nouveau contrôleur `AdminLicenseUpdateController` pour la gestion des permissions
  - Routes admin complètes pour toutes les fonctionnalités de mise à jour
  - Système de validation et upload de fichiers sécurisé
  - Interface admin complète pour la gestion du système de mise à jour

- **2025-01-06 19:00** - Système de mise à jour automatique - Stores Pinia frontend
  - Nouveau store `useVersionsStore` pour la gestion des versions CMS avec CRUD complet
  - Nouveau store `useUpdatesStore` pour la gestion des mises à jour d'installations
  - Nouveau store `useLicenseUpdatesStore` pour la gestion des permissions de licence
  - Intégration complète avec les APIs backend via ApiService
  - Gestion d'état réactive avec Vue 3 Composition API et TypeScript
  - Système de notifications intégré pour toutes les actions
  - Gestion d'erreurs robuste avec logs détaillés
  - Pagination, filtrage et recherche selon les patterns TechCMS existants

- **2025-01-06 20:30** - Système de mise à jour automatique - Vue dashboard et corrections critiques
  - Nouvelle vue `UpdatesDashboardView.vue` avec statistiques complètes et monitoring temps réel
  - Correction des erreurs PHP fatales d'autoloading des classes (AdminVersionController, UpdateFileService)
  - Ajout des traductions anglaises complètes dans `en.json` pour cohérence multilingue
  - Interface dashboard responsive avec auto-refresh et filtrage par période
  - Intégration parfaite avec les stores Pinia et design system TechCMS
  - API backend validée et fonctionnelle (endpoint /api/v1/updates/ping opérationnel)

- **2025-01-06 21:30** - Système de mise à jour automatique - Interface admin complète
  - Intégration complète dans la navigation avec nouvelle section "Mises à jour" dans la sidebar
  - Nouvelles vues Vue.js : `VersionsView.vue`, `UpdatesView.vue`, `LicenseUpdatesView.vue`
  - Routage complet avec 4 routes protégées et guards d'authentification
  - Gestion CRUD des versions CMS avec upload de fichiers et validation
  - Monitoring temps réel des mises à jour avec auto-refresh et filtrage avancé
  - Gestion des permissions de licence avec actions en lot et historique détaillé
  - Interface responsive respectant le design system TechCMS existant
  - Traductions complètes français/anglais pour tous les éléments d'interface

- **2025-01-06 22:30** - Système de mise à jour automatique - Composants spécialisés et corrections critiques
  - Création des 4 composants Vue.js spécialisés manquants (UpdateDetailsModal, VersionFormModal, FileUploadModal, LicenseHistoryModal)
  - Résolution des erreurs de build Vite avec tous les imports fonctionnels
  - Correction des erreurs PHP fatales d'accès aux propriétés protégées $db dans les contrôleurs
  - Utilisation du pattern TechCMS existant Database::getInstance()->getConnection()
  - 13 erreurs d'accès direct corrigées dans AdminUpdateController, AdminLicenseUpdateController et AdminVersionController
  - Interface utilisateur complète avec drag&drop, progress bars, timeline et validation
  - 150+ nouvelles traductions pour tous les composants spécialisés
  - APIs backend entièrement fonctionnelles et testées

- **2025-01-06 23:00** - Système de mise à jour automatique - Corrections finales SQL et permissions
  - Correction de l'erreur SQL "Column not found: update_permission" dans les APIs de permissions
  - Harmonisation du nommage : update_permission (code) → update_permissions (BDD)
  - Correction des permissions de fichiers pour le dossier storage/cms-updates/
  - 19 occurrences SQL corrigées au total dans 6 fichiers (AdminUpdateController, AdminLicenseUpdateController, LicenseModel, UpdateDownloadModel, UpdateDownloadService)
  - Recherche exhaustive avec grep pour éliminer toutes les erreurs SQL restantes
  - APIs de gestion des permissions de licence entièrement opérationnelles
  - Logs d'erreur nettoyés et système de mise à jour automatique 100% fonctionnel

- **2025-01-07 03:00** - Améliorations workflow et corrections UX critiques
  - SUPPRESSION page redondante LicenseUpdatesView.vue et route /system/updates/permissions
  - INTÉGRATION permissions de mise à jour dans le modal principal de gestion des licences
  - AJOUT toggle "Autoriser les mises à jour automatiques" dans LicenseFormModal.vue
  - CORRECTION modal VersionFormModal.vue - boutons de création/sauvegarde maintenant visibles
  - MISE À JOUR types TypeScript pour inclure update_permissions dans License interface
  - AJOUT traductions FR/EN pour les nouvelles fonctionnalités de permissions
  - SIMPLIFICATION architecture : permissions gérées directement dans CRUD licences principal
  - AMÉLIORATION UX : workflow de création de version clarifié avec boutons d'action visibles

- **2025-01-07 03:30** - Correction erreur PHP fatale et documentation technique complète
  - CORRECTION erreur PHP fatale dans AdminVersionController.php ligne 152
  - REMPLACEMENT new Validator() + validate() par Validator::make() + fails()
  - CORRECTION 2 occurrences dans méthodes create() et update() du contrôleur
  - CRÉATION documentation technique complète TECHCMS_UPDATE_SYSTEM_DOCUMENTATION.md
  - DOCUMENTATION architecture complète : backend, frontend, base de données
  - DOCUMENTATION 25+ endpoints API avec structures de données détaillées
  - DOCUMENTATION workflows complets pour administrateurs et intégration CMS
  - DOCUMENTATION guide d'intégration avec exemples de code PHP
  - DOCUMENTATION sécurité, déploiement, troubleshooting et maintenance
  - CLARIFICATION workflow création version : formulaire → validation → upload fichier séparé

- **2025-01-07 04:00** - Correction boutons d'actions invisibles dans DataTable
  - CORRECTION problème boutons d'actions invisibles dans liste des versions
  - MODIFICATION DataTable.vue pour supporter les slots nommés personnalisés
  - AJOUT gestion slots avec fallback vers formatter et valeur par défaut
  - AMÉLIORATION styles CSS pour boutons d'actions avec hover effects
  - CORRECTION affichage boutons : Modifier (bleu), Upload (bleu clair), Supprimer (rouge)
  - RÉSOLUTION problème accès interface upload de fichiers ZIP
  - VALIDATION workflow complet : création version → boutons visibles → upload fonctionnel

- **2025-01-07 04:30** - Optimisation interface upload : limite 1GB + modal opaque + bouton envoi
  - AUGMENTATION limite upload de 100MB à 1GB dans code JavaScript et traductions
  - CORRECTION modal transparent → modal opaque avec fond rgba(0,0,0,0.75)
  - AMÉLIORATION contraste modal avec background blanc et bordure définie
  - RESTRUCTURATION FileUploadModal pour éviter conflit de footers
  - AJOUT boutons d'action visibles : Annuler + Télécharger avec icônes
  - AMÉLIORATION UX avec badge version et styles responsive
  - CORRECTION structure template pour éliminer erreurs de validation
  - OPTIMISATION workflow upload avec feedback visuel renforcé

- **2025-01-07 05:00** - Timeout gros fichiers + Adaptation mode sombre
  - AUGMENTATION timeout API de 30s à 120s (2 minutes) pour gros fichiers
  - RÉSOLUTION timeout "30000ms exceeded" pour uploads 140MB+
  - ADAPTATION complète modal upload au mode sombre
  - UTILISATION variables CSS --bg-primary, --border-color, --text-color
  - AJOUT media query @prefers-color-scheme: dark pour auto-détection
  - AMÉLIORATION contraste file-preview et requirements-section en mode sombre
  - OPTIMISATION version-badge avec --primary-contrast pour lisibilité
  - SUPPORT complet thème sombre/clair avec transitions fluides

- **2025-01-07 05:30** - Progress bar temps réel + Statistiques upload avancées
  - IMPLÉMENTATION progress bar circulaire temps réel visible pendant upload
  - CORRECTION logique affichage : progress bar maintenant visible pendant upload
  - AJOUT statistiques avancées : temps écoulé, vitesse upload, temps restant estimé
  - AMÉLIORATION UX avec progress bar 100x100px et stroke-width 6px
  - AJOUT méthodes formatSpeed() et formatTime() pour affichage lisible
  - INTÉGRATION système finishUpload() pour coordination parent-enfant
  - OPTIMISATION timing : progression réaliste 400ms + stats temps réel 1s
  - DESIGN statistiques avec badges arrondis et icônes FontAwesome
  - RESPONSIVE progress bar avec adaptation mobile et mode sombre

- **2025-01-07 06:00** - VRAIES statistiques upload avec XMLHttpRequest
  - CRÉATION service uploadService.ts avec XMLHttpRequest et onUploadProgress
  - IMPLÉMENTATION vraies statistiques temps réel basées sur bytes réellement transférés
  - REMPLACEMENT simulation par vraie progression avec event.loaded/event.total
  - CALCUL vitesse réelle avec moyenne mobile sur 10 échantillons pour lissage
  - AJOUT timeout 300s (5 minutes) pour très gros fichiers
  - INTÉGRATION authentification Bearer token automatique
  - AFFICHAGE bytes transférés : "45.2 MB / 136.8 MB" en temps réel
  - GESTION erreurs réseau, timeout, parsing avec callbacks dédiés
  - COORDINATION parent-enfant via événement upload-success
  - SUPPRESSION simulation fake au profit de vraies données XMLHttpRequest

- **2025-01-07 06:30** - API Upload Chunked complète pour fichiers +120MB
  - CRÉATION ChunkedUploadService.php backend avec gestion complète chunks
  - IMPLÉMENTATION 3 phases : init_chunked_upload, upload_chunk, finalize_chunked_upload
  - MODIFICATION AdminVersionController pour supporter uploads chunked
  - DÉTECTION automatique : fichiers >70MB → chunked, ≤70MB → normal
  - ASSEMBLAGE fichiers par chunks de 50MB avec vérification intégrité
  - GESTION métadonnées upload avec JSON et nettoyage automatique
  - VALIDATION taille finale vs attendue + calcul SHA256 final
  - TIMEOUT 2 minutes par chunk (vs 60s global) pour éviter timeouts serveur
  - NETTOYAGE automatique uploads expirés (24h) et fichiers temporaires
  - LOGS détaillés progression chunk par chunk avec statistiques temps réel

- **2025-01-07 07:15** - Organisation fichiers par statut + génération download_url automatique
  - CRÉATION structure dossiers : cms-updates/{development,beta,stable,deprecated}
  - MODIFICATION UpdateFileService et ChunkedUploadService pour tri par statut
  - GÉNÉRATION automatique download_url : /api/v1/download/{version}
  - MIGRATION fichier existant techcms-1.1.0.zip vers /deprecated/
  - MISE À JOUR base données avec download_url pour versions existantes
  - ORGANISATION logique : Dev → Beta → Stable → Obsolète selon cycle de vie
  - SÉCURISATION accès fichiers via API au lieu de liens directs
  - COHÉRENCE upload normal et chunked avec même structure dossiers
  - AUTOMATISATION placement fichier selon statut version lors upload
  - PRÉPARATION téléchargements sécurisés avec tokens et permissions

- **2025-01-07 07:30** - Adaptation au système de tokens sécurisés existant
  - SUPPRESSION génération URLs statiques au profit du système de tokens
  - ADAPTATION UpdateFileService et ChunkedUploadService pour sécurité
  - MODIFICATION fileExists() et serveFile() pour recherche multi-dossiers
  - INTÉGRATION avec UpdateDownloadService existant pour tokens temporaires
  - WORKFLOW sécurisé : Licence → Token (24h) → Téléchargement tracé
  - PRÉPARATION vitrine commerciale avec vérification permissions
  - NETTOYAGE base données : download_url = NULL (généré dynamiquement)
  - COMPATIBILITÉ recherche fichiers dans tous dossiers de statut
  - SÉCURITÉ renforcée : pas d'accès direct aux fichiers
  - SYSTÈME commercial prêt pour contrôle d'accès par licence

### Modifié

### Corrigé

### Supprimé

---

## [1.1.0] - 2024-XX-XX

### Ajouté
- Système d'automatisation initial
- Structure de base pour les modules
- Gestion des licences et installations

### Modifié
- Amélioration de la structure de base de données

---

## [1.0.0] - 2024-XX-XX

### Ajouté
- Version initiale de TechCMS Commercial
- Gestion des clients et produits
- Système de facturation
- Interface d'administration
- API REST complète
